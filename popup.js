class PromptProcessor {
    constructor() {
        this.prompts = [];
        this.currentIndex = 0;
        this.isProcessing = false;
        this.isPaused = false;
        this.delay = 3000; // 3 seconds default
        this.autoScroll = true;
        this.processingPrompt = false; // Flag to prevent concurrent processing
        
        this.initializeElements();
        this.loadSettings();
        this.loadPrompts();
        this.bindEvents();
        this.updateUI();
    }
    
    initializeElements() {
        this.elements = {
            promptInput: document.getElementById('promptInput'),
            addPrompts: document.getElementById('addPrompts'),
            promptsList: document.getElementById('promptsList'),
            promptCount: document.getElementById('promptCount'),
            clearAll: document.getElementById('clearAll'),
            cleanDuplicates: document.getElementById('cleanDuplicates'),
            startWithWindow: document.getElementById('startWithWindow'),
            startProcessing: document.getElementById('startProcessing'),
            stopProcessing: document.getElementById('stopProcessing'),
            pauseProcessing: document.getElementById('pauseProcessing'),
            delayInput: document.getElementById('delayInput'),
            autoScrollCheckbox: document.getElementById('autoScroll'),
            status: document.getElementById('status'),
            progressFill: document.getElementById('progressFill'),
            progressText: document.getElementById('progressText')
        };
    }
    
    bindEvents() {
        // Prevent multiple event listeners by checking if already bound
        if (this.eventsBound) {
            console.log('⚠️ Events already bound, skipping');
            return;
        }

        console.log('🔗 Binding events...');

        this.elements.addPrompts.addEventListener('click', () => this.addPrompts());
        this.elements.clearAll.addEventListener('click', () => this.clearAllPrompts());
        this.elements.cleanDuplicates.addEventListener('click', () => this.cleanDuplicates());
        this.elements.startWithWindow.addEventListener('click', () => this.startProcessingWithWindow());
        this.elements.startProcessing.addEventListener('click', () => this.startProcessing());
        this.elements.stopProcessing.addEventListener('click', () => this.stopProcessing());
        this.elements.pauseProcessing.addEventListener('click', () => this.togglePause());
        this.elements.delayInput.addEventListener('change', () => this.updateDelay());
        this.elements.autoScrollCheckbox.addEventListener('change', () => this.updateAutoScroll());

        // Enter key in textarea
        this.elements.promptInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && e.ctrlKey) {
                e.preventDefault();
                this.addPrompts();
            }
        });

        // Listen for messages from content script
        browser.runtime.onMessage.addListener((message) => {
            if (message.type === 'promptProcessed') {
                this.onPromptProcessed(message.success, message.error);
            }
        });

        this.eventsBound = true;
        console.log('✅ Events bound successfully');
    }
    
    addPrompts() {
        console.log('📝 addPrompts() called');
        const input = this.elements.promptInput.value.trim();
        if (!input) {
            console.log('⚠️ No input text, returning');
            return;
        }

        console.log('📝 Input text:', input);
        console.log('📊 Current prompts count before adding:', this.prompts.length);

        // Split by lines or by --- separator
        const newPrompts = input.split(/\n|---/)
            .map(p => p.trim())
            .filter(p => p.length > 0);

        console.log('📝 New prompts to add:', newPrompts);

        // Check for duplicates before adding
        const existingTexts = new Set(this.prompts.map(p => p.text.trim()));

        newPrompts.forEach(prompt => {
            const trimmedPrompt = prompt.trim();

            if (existingTexts.has(trimmedPrompt)) {
                console.log('⚠️ Skipping duplicate prompt:', trimmedPrompt);
                return;
            }

            const newPrompt = {
                id: Date.now() + Math.random(),
                text: trimmedPrompt,
                status: 'pending'
            };
            console.log('➕ Adding new prompt:', newPrompt);
            this.prompts.push(newPrompt);
            existingTexts.add(trimmedPrompt);
        });

        console.log('📊 Total prompts count after adding:', this.prompts.length);

        this.elements.promptInput.value = '';
        this.savePrompts();
        this.updateUI();
    }
    
    removePrompt(id) {
        this.prompts = this.prompts.filter(p => p.id !== id);
        this.savePrompts();
        this.updateUI();
    }
    
    clearAllPrompts() {
        this.prompts = [];
        this.currentIndex = 0;
        this.savePrompts();
        this.updateUI();
    }

    cleanDuplicates() {
        console.log('🧹 Cleaning duplicates manually...');
        this.removeDuplicatePrompts();
        this.updateUI();
    }
    
    async startProcessingWithWindow() {
        console.log('🚀 Start processing with window clicked');

        if (this.prompts.length === 0) {
            this.updateStatus('No prompts to process', 'error');
            return;
        }

        // Get active tab
        const tabs = await browser.tabs.query({ active: true, currentWindow: true });
        if (tabs.length === 0) {
            this.updateStatus('No active tab found', 'error');
            return;
        }

        const tab = tabs[0];
        console.log('📍 Current tab:', tab.url);

        // Check if tab is compatible
        if (!this.isCompatibleTab(tab.url)) {
            this.updateStatus('This page is not supported. Please go to runware.ai', 'error');
            return;
        }

        // Test if content script is loaded
        console.log('🔍 Testing content script connection...');
        try {
            const testResponse = await this.sendMessageWithTimeout(tab.id, {
                type: 'ping'
            }, 3000);

            console.log('📡 Content script test response:', testResponse);

            if (!testResponse || testResponse.error) {
                throw new Error('Content script not responding');
            }
        } catch (error) {
            console.error('❌ Content script connection failed:', error);
            this.updateStatus('Content script not loaded. Please refresh the page and try again.', 'error');
            return;
        }

        console.log('✅ Content script is ready, starting processing with window...');

        // Send prompts to background script to start processing with window
        try {
            const response = await browser.runtime.sendMessage({
                type: 'startProcessingWithWindow',
                data: {
                    prompts: this.prompts,
                    tabId: tab.id,
                    delay: this.delay,
                    autoScroll: this.autoScroll
                }
            });

            if (response.success) {
                this.updateStatus('Processing window opened. You can close this popup.', 'processing');
                // Optionally close the popup after a delay
                setTimeout(() => {
                    window.close();
                }, 2000);
            } else {
                this.updateStatus('Failed to open processing window: ' + response.error, 'error');
            }
        } catch (error) {
            console.error('❌ Error starting processing with window:', error);
            this.updateStatus('Error starting processing: ' + error.message, 'error');
        }
    }

    async startProcessing() {
        console.log('🚀 Start processing clicked');

        if (this.prompts.length === 0) {
            this.updateStatus('No prompts to process', 'error');
            return;
        }

        // Get active tab
        const tabs = await browser.tabs.query({ active: true, currentWindow: true });
        if (tabs.length === 0) {
            this.updateStatus('No active tab found', 'error');
            return;
        }

        const tab = tabs[0];
        console.log('📍 Current tab:', tab.url);

        // Check if tab is compatible
        if (!this.isCompatibleTab(tab.url)) {
            this.updateStatus('This page is not supported. Please go to runware.ai', 'error');
            return;
        }

        // Test if content script is loaded
        console.log('🔍 Testing content script connection...');
        try {
            const testResponse = await this.sendMessageWithTimeout(tab.id, {
                type: 'ping'
            }, 3000);

            console.log('📡 Content script test response:', testResponse);

            if (!testResponse || testResponse.error) {
                throw new Error('Content script not responding');
            }
        } catch (error) {
            console.error('❌ Content script connection failed:', error);
            this.updateStatus('Content script not loaded. Please refresh the page and try again.', 'error');
            return;
        }

        console.log('✅ Content script is ready, starting processing...');

        this.isProcessing = true;
        this.isPaused = false;
        this.processingPrompt = false; // Reset processing flag
        this.currentIndex = 0;
        this.completedCount = 0;
        this.failedCount = 0;

        // Reset all prompts to pending
        this.prompts.forEach(prompt => {
            prompt.status = 'pending';
            delete prompt.error;
        });

        this.updateUI();
        this.updateStatus('Processing prompts...', 'processing');
        this.processNextPrompt(tab.id);
    }
    
    async processNextPrompt(tabId) {
        console.log(`🔄 processNextPrompt called - currentIndex: ${this.currentIndex}, isProcessing: ${this.isProcessing}, processingPrompt: ${this.processingPrompt}`);

        if (!this.isProcessing || this.isPaused) {
            console.log('⏹️ Processing stopped or paused, exiting');
            return;
        }

        if (this.processingPrompt) {
            console.log('⚠️ Already processing a prompt, skipping');
            return;
        }

        if (this.currentIndex >= this.prompts.length) {
            console.log('✅ All prompts completed');
            this.completeProcessing();
            return;
        }

        // Set processing flag to prevent concurrent processing
        this.processingPrompt = true;

        const prompt = this.prompts[this.currentIndex];
        console.log(`🚀 Starting to process prompt ${this.currentIndex + 1}: "${prompt.text.substring(0, 50)}..."`);

        prompt.status = 'processing';
        this.updateUI();

        try {
            // Send message to content script with timeout
            console.log('📤 Sending message to content script...');
            const response = await this.sendMessageWithTimeout(tabId, {
                type: 'processPrompt',
                prompt: prompt.text,
                autoScroll: this.autoScroll
            }, 120000); // 2 minute timeout for generation

            console.log('📥 Received response from content script:', response);

            // Handle the response
            if (response && response.success) {
                console.log('✅ Prompt processed successfully');
                this.onPromptProcessed(true);
            } else {
                console.log('❌ Prompt processing failed:', response?.error);
                this.onPromptProcessed(false, response?.error || 'Unknown error occurred');
            }

        } catch (error) {
            console.error('❌ Error processing prompt:', error);
            this.onPromptProcessed(false, error.message);
        } finally {
            // Always clear the processing flag
            this.processingPrompt = false;
        }
    }

    async sendMessageWithTimeout(tabId, message, timeout = 10000) {
        return new Promise((resolve, reject) => {
            const timer = setTimeout(() => {
                reject(new Error('Message timeout - content script may not be responding'));
            }, timeout);

            browser.tabs.sendMessage(tabId, message)
                .then(response => {
                    clearTimeout(timer);
                    resolve(response);
                })
                .catch(error => {
                    clearTimeout(timer);
                    reject(error);
                });
        });
    }

    isCompatibleTab(url) {
        if (!url) return false;

        const supportedDomains = [
            'runware.ai',
            'my.runware.ai',
            'playground',
            'openai.com',
            'chat.openai.com',
            'claude.ai',
            'bard.google.com'
        ];

        return supportedDomains.some(domain => url.includes(domain));
    }
    
    onPromptProcessed(success, error) {
        console.log(`📊 Prompt ${this.currentIndex + 1} processed:`, { success, error });

        if (this.currentIndex < this.prompts.length) {
            const prompt = this.prompts[this.currentIndex];
            prompt.status = success ? 'completed' : 'error';
            prompt.error = error;

            if (success) {
                this.completedCount++;
                console.log('✅ Prompt completed successfully');
            } else {
                this.failedCount++;
                console.log('❌ Prompt failed:', error);
            }
        }

        this.updateUI();

        if (success) {
            this.currentIndex++;
            if (this.isProcessing && !this.isPaused && this.currentIndex < this.prompts.length) {
                console.log(`⏳ Waiting ${this.delay}ms before next prompt...`);
                setTimeout(() => {
                    browser.tabs.query({ active: true, currentWindow: true }).then(tabs => {
                        if (tabs.length > 0) {
                            console.log('🔄 Processing next prompt...');
                            this.processNextPrompt(tabs[0].id);
                        }
                    });
                }, this.delay);
            } else if (this.currentIndex >= this.prompts.length) {
                console.log('🏁 All prompts processed, completing...');
                this.completeProcessing();
            }
        } else {
            // On failure, stop processing
            console.log('🛑 Stopping due to error');
            this.stopProcessing();
        }
    }
    
    stopProcessing() {
        this.isProcessing = false;
        this.isPaused = false;
        this.currentIndex = 0;
        
        // Reset all prompts to pending
        this.prompts.forEach(prompt => {
            if (prompt.status === 'processing') {
                prompt.status = 'pending';
            }
        });
        
        this.updateUI();
        this.updateStatus('Stopped', 'ready');
    }
    
    togglePause() {
        this.isPaused = !this.isPaused;
        this.updateUI();
        this.updateStatus(this.isPaused ? 'Paused' : 'Processing...', 'processing');
        
        if (!this.isPaused && this.isProcessing) {
            browser.tabs.query({ active: true, currentWindow: true }).then(tabs => {
                if (tabs.length > 0) {
                    this.processNextPrompt(tabs[0].id);
                }
            });
        }
    }
    
    completeProcessing() {
        this.isProcessing = false;
        this.isPaused = false;
        this.updateUI();
        this.updateStatus('All prompts processed!', 'ready');
    }
    
    updateDelay() {
        this.delay = parseInt(this.elements.delayInput.value) * 1000;
        this.saveSettings();
    }
    
    updateAutoScroll() {
        this.autoScroll = this.elements.autoScrollCheckbox.checked;
        this.saveSettings();
    }
    
    updateUI() {
        // Update prompt count
        this.elements.promptCount.textContent = this.prompts.length;
        
        // Update prompts list
        this.renderPromptsList();
        
        // Update buttons
        const hasPrompts = this.prompts.length > 0;
        const canStart = hasPrompts && !this.isProcessing;

        this.elements.startWithWindow.disabled = !canStart;
        this.elements.startProcessing.disabled = !canStart;
        this.elements.startWithWindow.style.display = this.isProcessing ? 'none' : 'block';
        this.elements.startProcessing.style.display = this.isProcessing ? 'none' : 'block';
        this.elements.stopProcessing.style.display = this.isProcessing ? 'block' : 'none';
        this.elements.pauseProcessing.style.display = this.isProcessing ? 'block' : 'none';
        this.elements.pauseProcessing.textContent = this.isPaused ? 'Resume' : 'Pause';
        
        // Update progress
        this.updateProgress();
    }
    
    renderPromptsList() {
        console.log('🎨 renderPromptsList() called with', this.prompts.length, 'prompts');

        if (this.prompts.length === 0) {
            this.elements.promptsList.innerHTML = '<div class="empty-state">No prompts added yet</div>';
            return;
        }

        // Check for duplicate prompts
        const uniqueTexts = new Set();
        const duplicates = [];
        this.prompts.forEach((prompt, index) => {
            if (uniqueTexts.has(prompt.text)) {
                duplicates.push({ index, text: prompt.text });
            } else {
                uniqueTexts.add(prompt.text);
            }
        });

        if (duplicates.length > 0) {
            console.warn('⚠️ Found duplicate prompts:', duplicates);
        }

        this.elements.promptsList.innerHTML = this.prompts.map((prompt, index) => `
            <div class="prompt-item ${prompt.status}" data-id="${prompt.id}">
                <div class="prompt-text" title="${prompt.text}">
                    ${index + 1}. ${prompt.text}
                    ${prompt.error ? ` (Error: ${prompt.error})` : ''}
                </div>
                <button class="prompt-remove" onclick="processor.removePrompt(${prompt.id})" title="Remove">×</button>
            </div>
        `).join('');
    }
    
    updateProgress() {
        const completed = this.prompts.filter(p => p.status === 'completed').length;
        const total = this.prompts.length;
        const percentage = total > 0 ? (completed / total) * 100 : 0;
        
        this.elements.progressFill.style.width = `${percentage}%`;
        this.elements.progressText.textContent = `${completed} / ${total} completed`;
    }
    
    updateStatus(message, type = 'ready') {
        this.elements.status.textContent = message;
        this.elements.status.className = `status ${type}`;
    }
    
    savePrompts() {
        browser.storage.local.set({ prompts: this.prompts });
    }
    
    async loadPrompts() {
        console.log('📂 loadPrompts() called');
        const result = await browser.storage.local.get('prompts');
        const loadedPrompts = result.prompts || [];
        console.log('📂 Loaded prompts from storage:', loadedPrompts);
        console.log('📊 Current prompts before loading:', this.prompts.length);
        this.prompts = loadedPrompts;

        // Remove duplicates
        this.removeDuplicatePrompts();

        console.log('📊 Prompts after loading and deduplication:', this.prompts.length);
    }

    removeDuplicatePrompts() {
        const seen = new Set();
        const originalLength = this.prompts.length;

        this.prompts = this.prompts.filter(prompt => {
            // Normalize text by trimming and removing trailing punctuation
            const normalizedText = prompt.text.trim().replace(/[,\s]*$/, '');

            if (seen.has(normalizedText)) {
                console.log('🗑️ Removing duplicate prompt:', prompt.text);
                return false;
            }
            seen.add(normalizedText);
            return true;
        });

        if (originalLength !== this.prompts.length) {
            console.log(`🧹 Removed ${originalLength - this.prompts.length} duplicate prompts`);
            this.savePrompts(); // Save the cleaned list
        }
    }
    
    saveSettings() {
        browser.storage.local.set({
            delay: this.delay,
            autoScroll: this.autoScroll
        });
    }
    
    async loadSettings() {
        const result = await browser.storage.local.get(['delay', 'autoScroll']);
        this.delay = result.delay || 3000;
        this.autoScroll = result.autoScroll !== undefined ? result.autoScroll : true;
        
        this.elements.delayInput.value = this.delay / 1000;
        this.elements.autoScrollCheckbox.checked = this.autoScroll;
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.processor = new PromptProcessor();
});
