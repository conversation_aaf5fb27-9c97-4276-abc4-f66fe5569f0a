<!DOCTYPE html>
<html>
<head>
    <title>Test Page for Prompt Processor Extension</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .container {
            background: #f5f5f5;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        textarea {
            width: 100%;
            height: 150px;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            resize: vertical;
        }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px 5px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        .output {
            background: white;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
            min-height: 100px;
        }
        
        .log {
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <h1>Test Page for Prompt Processor Extension</h1>
    <p>This page simulates an AI interface like runware.ai for testing the extension.</p>
    
    <div class="container">
        <h2>AI Prompt Interface</h2>
        <textarea id="promptTextarea" placeholder="Enter your prompt here..."></textarea>
        <br>
        <button id="generateBtn">Generate</button>
        <button id="sendBtn">Send</button>
        <button id="submitBtn">Submit</button>
        <button onclick="clearOutput()">Clear Output</button>
    </div>
    
    <div class="output" id="output">
        <h3>Output:</h3>
        <div id="outputContent">Ready to receive prompts...</div>
    </div>
    
    <div class="log" id="log">
        <strong>Activity Log:</strong><br>
        <div id="logContent">Page loaded and ready for testing.</div>
    </div>

    <script>
        let promptCount = 0;
        
        function log(message) {
            const logContent = document.getElementById('logContent');
            const timestamp = new Date().toLocaleTimeString();
            logContent.innerHTML += `<br>[${timestamp}] ${message}`;
            logContent.scrollTop = logContent.scrollHeight;
        }
        
        function simulateProcessing(prompt) {
            log(`Processing prompt: "${prompt.substring(0, 50)}${prompt.length > 50 ? '...' : ''}"`);
            
            // Simulate processing time
            setTimeout(() => {
                const outputContent = document.getElementById('outputContent');
                promptCount++;
                
                const result = `
                    <div style="border: 1px solid #ddd; padding: 10px; margin: 10px 0; border-radius: 5px;">
                        <strong>Prompt #${promptCount}:</strong><br>
                        <em>${prompt}</em><br><br>
                        <strong>Generated Response:</strong><br>
                        This is a simulated response to your prompt. In a real AI interface, this would be the actual generated content.
                    </div>
                `;
                
                outputContent.innerHTML += result;
                log(`Completed processing prompt #${promptCount}`);
                
                // Scroll to show the new content
                outputContent.scrollTop = outputContent.scrollHeight;
            }, 1000 + Math.random() * 2000); // Random delay between 1-3 seconds
        }
        
        function clearOutput() {
            document.getElementById('outputContent').innerHTML = 'Output cleared...';
            document.getElementById('logContent').innerHTML = 'Log cleared...';
            promptCount = 0;
            log('Output and log cleared');
        }
        
        // Add event listeners to all buttons
        document.getElementById('generateBtn').addEventListener('click', function() {
            const textarea = document.getElementById('promptTextarea');
            const prompt = textarea.value.trim();
            
            if (prompt) {
                log('Generate button clicked');
                simulateProcessing(prompt);
                textarea.value = ''; // Clear the textarea after processing
            } else {
                log('Generate button clicked but no prompt found');
            }
        });
        
        document.getElementById('sendBtn').addEventListener('click', function() {
            const textarea = document.getElementById('promptTextarea');
            const prompt = textarea.value.trim();
            
            if (prompt) {
                log('Send button clicked');
                simulateProcessing(prompt);
                textarea.value = ''; // Clear the textarea after processing
            } else {
                log('Send button clicked but no prompt found');
            }
        });
        
        document.getElementById('submitBtn').addEventListener('click', function() {
            const textarea = document.getElementById('promptTextarea');
            const prompt = textarea.value.trim();
            
            if (prompt) {
                log('Submit button clicked');
                simulateProcessing(prompt);
                textarea.value = ''; // Clear the textarea after processing
            } else {
                log('Submit button clicked but no prompt found');
            }
        });
        
        // Also handle Ctrl+Enter
        document.getElementById('promptTextarea').addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'Enter') {
                e.preventDefault();
                document.getElementById('generateBtn').click();
            }
        });
        
        log('Test page initialized. You can now test the Prompt Processor extension.');
        log('Make sure to load the extension in Firefox first, then click the extension icon.');
    </script>
</body>
</html>
