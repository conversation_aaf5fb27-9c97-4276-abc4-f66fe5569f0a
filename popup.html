<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Prompt Processor</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>Prompt Processor</h2>
            <div class="status" id="status">Ready</div>
        </div>
        
        <div class="input-section">
            <textarea 
                id="promptInput" 
                placeholder="Enter your prompts here (one per line or separated by ---)"
                rows="6"
            ></textarea>
            <button id="addPrompts" class="btn btn-primary">Add Prompts</button>
        </div>
        
        <div class="prompts-section">
            <div class="section-header">
                <h3>Prompt Queue (<span id="promptCount">0</span>)</h3>
                <div class="header-buttons">
                    <button id="cleanDuplicates" class="btn btn-small">Clean Duplicates</button>
                    <button id="clearAll" class="btn btn-small">Clear All</button>
                </div>
            </div>
            <div id="promptsList" class="prompts-list"></div>
        </div>
        
        <div class="controls">
            <button id="startWithWindow" class="btn btn-primary" disabled>
                🚀 Start in Window
            </button>
            <button id="startProcessing" class="btn btn-success" disabled>
                Start Processing
            </button>
            <button id="stopProcessing" class="btn btn-danger" style="display: none;">
                Stop Processing
            </button>
            <button id="pauseProcessing" class="btn btn-warning" style="display: none;">
                Pause
            </button>
        </div>
        
        <div class="settings">
            <div class="setting-item">
                <label for="delayInput">Delay between prompts (seconds):</label>
                <input type="number" id="delayInput" min="1" max="60" value="3">
            </div>
            <div class="setting-item">
                <label>
                    <input type="checkbox" id="autoScroll"> Auto-scroll to textarea
                </label>
            </div>
        </div>
        
        <div class="progress">
            <div class="progress-bar">
                <div id="progressFill" class="progress-fill"></div>
            </div>
            <div id="progressText" class="progress-text">0 / 0 completed</div>
        </div>
    </div>
    
    <script src="popup.js"></script>
</body>
</html>
