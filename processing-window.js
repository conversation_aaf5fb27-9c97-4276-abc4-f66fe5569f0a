class ProcessingWindow {
    constructor() {
        this.isProcessing = false;
        this.isPaused = false;
        this.totalPrompts = 0;
        this.completedCount = 0;
        this.failedCount = 0;
        this.currentPromptIndex = 0;
        this.currentPromptText = '';
        
        this.initializeElements();
        this.bindEvents();
        this.setupMessageListener();
        
        // Request initial status from background
        this.requestStatus();
    }
    
    initializeElements() {
        this.elements = {
            status: document.getElementById('status'),
            progressFill: document.getElementById('progressFill'),
            progressText: document.getElementById('progressText'),
            completedCount: document.getElementById('completedCount'),
            failedCount: document.getElementById('failedCount'),
            remainingCount: document.getElementById('remainingCount'),
            currentPrompt: document.getElementById('currentPrompt'),
            currentPromptText: document.getElementById('currentPromptText'),
            pauseBtn: document.getElementById('pauseBtn'),
            stopBtn: document.getElementById('stopBtn'),
            closeBtn: document.getElementById('closeBtn')
        };
    }
    
    bindEvents() {
        this.elements.pauseBtn.addEventListener('click', () => {
            this.togglePause();
        });
        
        this.elements.stopBtn.addEventListener('click', () => {
            this.stopProcessing();
        });
        
        this.elements.closeBtn.addEventListener('click', () => {
            window.close();
        });
        
        // Handle window close event
        window.addEventListener('beforeunload', () => {
            // Notify background that processing window is closing
            browser.runtime.sendMessage({
                type: 'processingWindowClosed'
            });
        });
    }
    
    setupMessageListener() {
        browser.runtime.onMessage.addListener((message, sender, sendResponse) => {
            console.log('Processing window received message:', message);
            
            switch (message.type) {
                case 'processingStatus':
                    this.updateStatus(message.data);
                    break;
                    
                case 'processingStarted':
                    this.onProcessingStarted(message.data);
                    break;
                    
                case 'promptProcessed':
                    this.onPromptProcessed(message.data);
                    break;
                    
                case 'processingCompleted':
                    this.onProcessingCompleted(message.data);
                    break;
                    
                case 'processingStopped':
                    this.onProcessingStopped();
                    break;
                    
                case 'processingPaused':
                    this.onProcessingPaused(message.isPaused);
                    break;
            }
        });
    }
    
    requestStatus() {
        browser.runtime.sendMessage({
            type: 'getProcessingStatus'
        });
    }
    
    updateStatus(data) {
        this.isProcessing = data.isProcessing;
        this.isPaused = data.isPaused;
        this.totalPrompts = data.totalPrompts;
        this.completedCount = data.completedCount;
        this.failedCount = data.failedCount;
        this.currentPromptIndex = data.currentPromptIndex;
        this.currentPromptText = data.currentPromptText;
        
        this.updateUI();
    }
    
    onProcessingStarted(data) {
        this.isProcessing = true;
        this.isPaused = false;
        this.totalPrompts = data.totalPrompts;
        this.completedCount = 0;
        this.failedCount = 0;
        this.currentPromptIndex = 0;
        
        this.updateUI();
        this.updateStatusMessage('Processing prompts...', 'processing');
    }
    
    onPromptProcessed(data) {
        this.completedCount = data.completedCount;
        this.failedCount = data.failedCount;
        this.currentPromptIndex = data.currentPromptIndex;
        this.currentPromptText = data.currentPromptText;
        
        this.updateUI();
        
        if (data.success) {
            this.updateStatusMessage(`Processing prompt ${this.currentPromptIndex + 1} of ${this.totalPrompts}...`, 'processing');
        } else {
            this.updateStatusMessage(`Error processing prompt: ${data.error}`, 'error');
        }
    }
    
    onProcessingCompleted(data) {
        this.isProcessing = false;
        this.isPaused = false;
        this.completedCount = data.completedCount;
        this.failedCount = data.failedCount;
        
        this.updateUI();
        this.updateStatusMessage(`Processing completed! ${this.completedCount} successful, ${this.failedCount} failed.`, 'completed');
        
        // Show close button
        this.elements.closeBtn.style.display = 'block';
        this.elements.pauseBtn.style.display = 'none';
        this.elements.stopBtn.style.display = 'none';
        
        // Show notification
        if (browser.notifications) {
            browser.notifications.create({
                type: 'basic',
                iconUrl: 'icons/icon-48.png',
                title: 'Prompt Processing Completed!',
                message: `${this.completedCount} prompts processed successfully, ${this.failedCount} failed.`
            });
        }
    }
    
    onProcessingStopped() {
        this.isProcessing = false;
        this.isPaused = false;
        
        this.updateUI();
        this.updateStatusMessage('Processing stopped by user.', 'error');
        
        // Show close button
        this.elements.closeBtn.style.display = 'block';
        this.elements.pauseBtn.style.display = 'none';
        this.elements.stopBtn.style.display = 'none';
    }
    
    onProcessingPaused(isPaused) {
        this.isPaused = isPaused;
        this.updateUI();
        this.updateStatusMessage(isPaused ? 'Processing paused.' : 'Processing resumed.', isPaused ? 'paused' : 'processing');
    }
    
    togglePause() {
        browser.runtime.sendMessage({
            type: 'toggleProcessingPause'
        });
    }
    
    stopProcessing() {
        browser.runtime.sendMessage({
            type: 'stopProcessing'
        });
    }
    
    updateUI() {
        // Update progress bar
        const percentage = this.totalPrompts > 0 ? (this.completedCount / this.totalPrompts) * 100 : 0;
        this.elements.progressFill.style.width = `${percentage}%`;
        this.elements.progressText.textContent = `${this.completedCount} / ${this.totalPrompts} completed`;
        
        // Update stats
        this.elements.completedCount.textContent = this.completedCount;
        this.elements.failedCount.textContent = this.failedCount;
        this.elements.remainingCount.textContent = this.totalPrompts - this.completedCount - this.failedCount;
        
        // Update current prompt
        if (this.isProcessing && this.currentPromptText) {
            this.elements.currentPrompt.style.display = 'block';
            this.elements.currentPromptText.textContent = this.currentPromptText;
        } else {
            this.elements.currentPrompt.style.display = 'none';
        }
        
        // Update buttons
        this.elements.pauseBtn.textContent = this.isPaused ? 'Resume' : 'Pause';
        this.elements.pauseBtn.style.display = this.isProcessing ? 'block' : 'none';
        this.elements.stopBtn.style.display = this.isProcessing ? 'block' : 'none';
    }
    
    updateStatusMessage(message, type) {
        this.elements.status.textContent = message;
        this.elements.status.className = `status ${type}`;
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.processingWindow = new ProcessingWindow();
});
