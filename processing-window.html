<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Prompt Processor - Processing</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            box-sizing: border-box;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            margin: 0 0 10px 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .status {
            font-size: 1.2em;
            margin-bottom: 20px;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            font-weight: 500;
        }
        
        .status.processing {
            background: rgba(52, 152, 219, 0.3);
            border: 2px solid #3498db;
        }
        
        .status.paused {
            background: rgba(241, 196, 15, 0.3);
            border: 2px solid #f1c40f;
        }
        
        .status.completed {
            background: rgba(46, 204, 113, 0.3);
            border: 2px solid #2ecc71;
        }
        
        .status.error {
            background: rgba(231, 76, 60, 0.3);
            border: 2px solid #e74c3c;
        }
        
        .progress-section {
            margin: 30px 0;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 15px;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #2ecc71, #27ae60);
            width: 0%;
            transition: width 0.3s ease;
            border-radius: 10px;
        }
        
        .progress-text {
            text-align: center;
            font-size: 1.1em;
            font-weight: 500;
        }
        
        .current-prompt {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #3498db;
        }
        
        .current-prompt h3 {
            margin: 0 0 10px 0;
            color: #3498db;
        }
        
        .prompt-text {
            font-size: 1em;
            line-height: 1.5;
            word-wrap: break-word;
        }
        
        .controls {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1em;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 120px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }
        
        .btn-pause {
            background: #f39c12;
            color: white;
        }
        
        .btn-stop {
            background: #e74c3c;
            color: white;
        }
        
        .btn-close {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .stat-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }
        
        .stat-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 0.9em;
            opacity: 0.8;
        }
        
        .minimize-notice {
            text-align: center;
            margin-top: 20px;
            padding: 15px;
            background: rgba(52, 152, 219, 0.2);
            border-radius: 10px;
            font-size: 0.9em;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
        
        .processing .progress-fill {
            animation: pulse 2s infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Processing Prompts</h1>
        </div>
        
        <div id="status" class="status processing">
            Initializing...
        </div>
        
        <div class="progress-section">
            <div class="progress-bar">
                <div id="progressFill" class="progress-fill"></div>
            </div>
            <div id="progressText" class="progress-text">0 / 0 completed</div>
        </div>
        
        <div class="stats">
            <div class="stat-item">
                <div id="completedCount" class="stat-value">0</div>
                <div class="stat-label">Completed</div>
            </div>
            <div class="stat-item">
                <div id="failedCount" class="stat-value">0</div>
                <div class="stat-label">Failed</div>
            </div>
            <div class="stat-item">
                <div id="remainingCount" class="stat-value">0</div>
                <div class="stat-label">Remaining</div>
            </div>
        </div>
        
        <div id="currentPrompt" class="current-prompt" style="display: none;">
            <h3>Currently Processing:</h3>
            <div id="currentPromptText" class="prompt-text"></div>
        </div>
        
        <div class="controls">
            <button id="pauseBtn" class="btn btn-pause">Pause</button>
            <button id="stopBtn" class="btn btn-stop">Stop</button>
            <button id="closeBtn" class="btn btn-close" style="display: none;">Close</button>
        </div>
        
        <div class="minimize-notice">
            💡 You can minimize this window and continue using your browser. 
            Processing will continue in the background.
        </div>
    </div>
    
    <script src="processing-window.js"></script>
</body>
</html>
