* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    width: 400px;
    min-height: 500px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #f8f9fa;
    color: #333;
}

.container {
    padding: 16px;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 2px solid #e9ecef;
}

.header h2 {
    color: #495057;
    font-size: 18px;
    font-weight: 600;
}

.status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    background: #d4edda;
    color: #155724;
}

.status.processing {
    background: #fff3cd;
    color: #856404;
}

.status.error {
    background: #f8d7da;
    color: #721c24;
}

.input-section {
    margin-bottom: 16px;
}

#promptInput {
    width: 100%;
    padding: 12px;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    font-size: 14px;
    font-family: inherit;
    resize: vertical;
    margin-bottom: 8px;
    transition: border-color 0.2s;
}

#promptInput:focus {
    outline: none;
    border-color: #007bff;
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    text-align: center;
    display: inline-block;
}

.btn:hover {
    transform: translateY(-1px);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: #0056b3;
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-success:hover:not(:disabled) {
    background: #1e7e34;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover:not(:disabled) {
    background: #c82333;
}

.btn-warning {
    background: #ffc107;
    color: #212529;
}

.btn-warning:hover:not(:disabled) {
    background: #e0a800;
}

.btn-small {
    padding: 4px 8px;
    font-size: 12px;
}

.prompts-section {
    margin-bottom: 16px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.section-header h3 {
    font-size: 14px;
    color: #495057;
}

.header-buttons {
    display: flex;
    gap: 8px;
}

.prompts-list {
    max-height: 150px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    background: white;
}

.prompt-item {
    padding: 8px 12px;
    border-bottom: 1px solid #f1f3f4;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 13px;
}

.prompt-item:last-child {
    border-bottom: none;
}

.prompt-item.completed {
    background: #d4edda;
    color: #155724;
}

.prompt-item.processing {
    background: #fff3cd;
    color: #856404;
}

.prompt-text {
    flex: 1;
    margin-right: 8px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.prompt-remove {
    background: none;
    border: none;
    color: #dc3545;
    cursor: pointer;
    font-size: 16px;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.controls {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 16px;
}

.controls .btn {
    flex: 1;
    min-width: 120px;
}

#startWithWindow {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-weight: 600;
}

#startWithWindow:hover:not(:disabled) {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.settings {
    margin-bottom: 16px;
    padding: 12px;
    background: white;
    border-radius: 6px;
    border: 1px solid #dee2e6;
}

.setting-item {
    margin-bottom: 8px;
}

.setting-item:last-child {
    margin-bottom: 0;
}

.setting-item label {
    display: block;
    font-size: 13px;
    color: #495057;
    margin-bottom: 4px;
}

.setting-item input[type="number"] {
    width: 60px;
    padding: 4px 8px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    font-size: 13px;
}

.setting-item input[type="checkbox"] {
    margin-right: 6px;
}

.progress {
    margin-top: 16px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 4px;
}

.progress-fill {
    height: 100%;
    background: #007bff;
    width: 0%;
    transition: width 0.3s ease;
}

.progress-text {
    font-size: 12px;
    color: #6c757d;
    text-align: center;
}

.empty-state {
    text-align: center;
    padding: 20px;
    color: #6c757d;
    font-size: 13px;
}

/* Scrollbar styling */
.prompts-list::-webkit-scrollbar {
    width: 6px;
}

.prompts-list::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.prompts-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.prompts-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
