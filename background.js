// Background script for Prompt Processor extension

class BackgroundManager {
    constructor() {
        this.processingWindowId = null;
        this.isProcessing = false;
        this.isPaused = false;
        this.processingData = {
            totalPrompts: 0,
            completedCount: 0,
            failedCount: 0,
            currentPromptIndex: 0,
            currentPromptText: '',
            prompts: []
        };
        this.init();
    }
    
    init() {
        // Handle extension installation
        browser.runtime.onInstalled.addListener((details) => {
            if (details.reason === 'install') {
                console.log('Prompt Processor extension installed');
                this.showWelcomeNotification();
            } else if (details.reason === 'update') {
                console.log('Prompt Processor extension updated');
            }
        });
        
        // Handle messages between popup and content scripts
        browser.runtime.onMessage.addListener((message, sender, sendResponse) => {
            console.log('Background received message:', message);
            
            switch (message.type) {
                case 'getActiveTab':
                    this.getActiveTab().then(sendResponse);
                    return true;
                    
                case 'checkTabCompatibility':
                    this.checkTabCompatibility(message.tabId).then(sendResponse);
                    return true;
                    
                case 'promptProcessed':
                    // Forward message to popup if it's open
                    this.forwardToPopup(message);
                    break;

                case 'ping':
                    console.log('🏓 Ping received from content script');
                    sendResponse({ success: true, message: 'Background script is working' });
                    return true;

                case 'keepAlive':
                    // Simple acknowledgment to keep popup connection alive
                    sendResponse({ alive: true, timestamp: Date.now() });
                    return true;

                case 'startProcessingWithWindow':
                    this.startProcessingWithWindow(message.data).then(sendResponse);
                    return true;

                case 'getProcessingStatus':
                    sendResponse({
                        isProcessing: this.isProcessing,
                        isPaused: this.isPaused,
                        ...this.processingData
                    });
                    return true;

                case 'toggleProcessingPause':
                    this.toggleProcessingPause();
                    break;

                case 'stopProcessing':
                    this.stopProcessing();
                    break;

                case 'processingWindowClosed':
                    this.processingWindowId = null;
                    break;

                case 'updateProcessingProgress':
                    this.updateProcessingProgress(message.data);
                    break;

                default:
                    console.log('Unknown message type:', message.type);
            }
        });
        
        // Handle tab updates to check compatibility
        browser.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
            if (changeInfo.status === 'complete' && tab.url) {
                this.updateBrowserActionState(tab);
            }
        });
        
        // Handle tab activation
        browser.tabs.onActivated.addListener((activeInfo) => {
            browser.tabs.get(activeInfo.tabId).then(tab => {
                this.updateBrowserActionState(tab);
            });
        });
        
        console.log('Prompt Processor background script initialized');
    }
    
    async getActiveTab() {
        try {
            const tabs = await browser.tabs.query({ active: true, currentWindow: true });
            return tabs.length > 0 ? tabs[0] : null;
        } catch (error) {
            console.error('Error getting active tab:', error);
            return null;
        }
    }
    
    async checkTabCompatibility(tabId) {
        try {
            const tab = await browser.tabs.get(tabId);
            const isCompatible = this.isCompatibleUrl(tab.url);
            
            return {
                compatible: isCompatible,
                url: tab.url,
                title: tab.title
            };
        } catch (error) {
            console.error('Error checking tab compatibility:', error);
            return { compatible: false, error: error.message };
        }
    }
    
    isCompatibleUrl(url) {
        if (!url) return false;
        
        const compatiblePatterns = [
            /runware\.ai/i,
            /playground/i,
            /openai\.com/i,
            /chat\.openai\.com/i,
            /claude\.ai/i,
            /bard\.google\.com/i,
            /huggingface\.co/i,
            /replicate\.com/i,
            /cohere\.ai/i,
            /anthropic\.com/i
        ];
        
        return compatiblePatterns.some(pattern => pattern.test(url));
    }
    
    async updateBrowserActionState(tab) {
        try {
            const isCompatible = this.isCompatibleUrl(tab.url);
            
            // Update browser action icon and title based on compatibility
            if (isCompatible) {
                await browser.browserAction.setIcon({
                    tabId: tab.id,
                    path: {
                        "16": "icons/icon-16.png",
                        "32": "icons/icon-32.png",
                        "48": "icons/icon-48.png",
                        "128": "icons/icon-128.png"
                    }
                });
                await browser.browserAction.setTitle({
                    tabId: tab.id,
                    title: "Prompt Processor - Ready to process prompts"
                });
            } else {
                await browser.browserAction.setIcon({
                    tabId: tab.id,
                    path: {
                        "16": "icons/icon-16-disabled.png",
                        "32": "icons/icon-32-disabled.png",
                        "48": "icons/icon-48-disabled.png",
                        "128": "icons/icon-128-disabled.png"
                    }
                });
                await browser.browserAction.setTitle({
                    tabId: tab.id,
                    title: "Prompt Processor - Not compatible with this page"
                });
            }
        } catch (error) {
            console.error('Error updating browser action state:', error);
        }
    }
    
    async forwardToPopup(message) {
        try {
            // Try to send message to popup
            await browser.runtime.sendMessage(message);
        } catch (error) {
            // Popup might not be open, which is fine
            console.log('Could not forward message to popup (popup might be closed)');
        }
    }
    
    showWelcomeNotification() {
        // Create a welcome notification
        if (browser.notifications) {
            browser.notifications.create({
                type: 'basic',
                iconUrl: 'icons/icon-48.png',
                title: 'Prompt Processor Installed!',
                message: 'Click the extension icon on AI websites to start processing prompts automatically.'
            });
        }
    }
    
    // Utility method to inject content script if needed
    async injectContentScript(tabId) {
        try {
            await browser.tabs.executeScript(tabId, {
                file: 'content.js'
            });
            console.log('Content script injected successfully');
        } catch (error) {
            console.error('Error injecting content script:', error);
            throw error;
        }
    }
    
    // Processing window management
    async startProcessingWithWindow(data) {
        try {
            // Store processing data
            this.processingData = {
                totalPrompts: data.prompts.length,
                completedCount: 0,
                failedCount: 0,
                currentPromptIndex: 0,
                currentPromptText: data.prompts[0]?.text || '',
                prompts: data.prompts,
                tabId: data.tabId,
                delay: data.delay,
                autoScroll: data.autoScroll
            };

            this.isProcessing = true;
            this.isPaused = false;

            // Create processing window
            const window = await browser.windows.create({
                url: 'processing-window.html',
                type: 'popup',
                width: 600,
                height: 700,
                focused: true
            });

            this.processingWindowId = window.id;

            // Start processing after a short delay to ensure window is ready
            setTimeout(() => {
                this.broadcastToProcessingWindow({
                    type: 'processingStarted',
                    data: this.processingData
                });
                // Start the actual processing
                this.processNextPrompt();
            }, 1000);

            return { success: true, windowId: window.id };
        } catch (error) {
            console.error('Error creating processing window:', error);
            return { success: false, error: error.message };
        }
    }

    async processNextPrompt() {
        if (!this.isProcessing || this.isPaused) {
            console.log('Processing stopped or paused');
            return;
        }

        if (this.processingData.currentPromptIndex >= this.processingData.totalPrompts) {
            console.log('All prompts completed');
            this.completeProcessing();
            return;
        }

        const prompt = this.processingData.prompts[this.processingData.currentPromptIndex];
        console.log(`Processing prompt ${this.processingData.currentPromptIndex + 1}: ${prompt.text.substring(0, 50)}...`);

        this.processingData.currentPromptText = prompt.text;

        try {
            // Send message to content script
            const response = await this.sendMessageWithTimeout(this.processingData.tabId, {
                type: 'processPrompt',
                prompt: prompt.text,
                autoScroll: this.processingData.autoScroll
            }, 120000); // 2 minute timeout

            if (response && response.success) {
                this.processingData.completedCount++;
                console.log('Prompt processed successfully');
            } else {
                this.processingData.failedCount++;
                console.log('Prompt processing failed:', response?.error);
            }

            this.processingData.currentPromptIndex++;

            // Update processing window
            this.broadcastToProcessingWindow({
                type: 'promptProcessed',
                data: {
                    ...this.processingData,
                    success: response?.success || false,
                    error: response?.error
                }
            });

            // Continue with next prompt after delay
            if (this.processingData.currentPromptIndex < this.processingData.totalPrompts) {
                setTimeout(() => {
                    this.processNextPrompt();
                }, this.processingData.delay);
            } else {
                this.completeProcessing();
            }

        } catch (error) {
            console.error('Error processing prompt:', error);
            this.processingData.failedCount++;
            this.processingData.currentPromptIndex++;

            this.broadcastToProcessingWindow({
                type: 'promptProcessed',
                data: {
                    ...this.processingData,
                    success: false,
                    error: error.message
                }
            });

            // Continue with next prompt
            setTimeout(() => {
                this.processNextPrompt();
            }, this.processingData.delay);
        }
    }

    async sendMessageWithTimeout(tabId, message, timeout = 10000) {
        return new Promise((resolve, reject) => {
            const timer = setTimeout(() => {
                reject(new Error('Message timeout - content script may not be responding'));
            }, timeout);

            browser.tabs.sendMessage(tabId, message)
                .then(response => {
                    clearTimeout(timer);
                    resolve(response);
                })
                .catch(error => {
                    clearTimeout(timer);
                    reject(error);
                });
        });
    }

    completeProcessing() {
        this.isProcessing = false;
        this.isPaused = false;

        this.broadcastToProcessingWindow({
            type: 'processingCompleted',
            data: this.processingData
        });

        console.log(`Processing completed: ${this.processingData.completedCount} successful, ${this.processingData.failedCount} failed`);
    }

    toggleProcessingPause() {
        this.isPaused = !this.isPaused;
        this.broadcastToProcessingWindow({
            type: 'processingPaused',
            isPaused: this.isPaused
        });

        // Forward to popup if open
        this.forwardToPopup({
            type: 'processingPaused',
            isPaused: this.isPaused
        });

        // Resume processing if unpaused
        if (!this.isPaused && this.isProcessing) {
            setTimeout(() => {
                this.processNextPrompt();
            }, 100);
        }
    }

    stopProcessing() {
        this.isProcessing = false;
        this.isPaused = false;

        this.broadcastToProcessingWindow({
            type: 'processingStopped'
        });

        // Forward to popup if open
        this.forwardToPopup({
            type: 'processingStopped'
        });
    }

    updateProcessingProgress(data) {
        this.processingData.completedCount = data.completedCount;
        this.processingData.failedCount = data.failedCount;
        this.processingData.currentPromptIndex = data.currentPromptIndex;
        this.processingData.currentPromptText = data.currentPromptText;

        this.broadcastToProcessingWindow({
            type: 'promptProcessed',
            data: this.processingData
        });

        // Check if processing is complete
        if (data.currentPromptIndex >= this.processingData.totalPrompts) {
            this.isProcessing = false;
            this.broadcastToProcessingWindow({
                type: 'processingCompleted',
                data: this.processingData
            });
        }
    }

    async broadcastToProcessingWindow(message) {
        if (this.processingWindowId) {
            try {
                await browser.tabs.query({ windowId: this.processingWindowId });
                await browser.runtime.sendMessage(message);
            } catch (error) {
                console.log('Could not send message to processing window:', error);
                this.processingWindowId = null;
            }
        }
    }

    // Handle context menu (optional feature)
    setupContextMenu() {
        browser.contextMenus.create({
            id: 'processSelectedText',
            title: 'Process selected text as prompt',
            contexts: ['selection']
        });

        browser.contextMenus.onClicked.addListener((info, tab) => {
            if (info.menuItemId === 'processSelectedText' && info.selectionText) {
                // Send selected text to content script for processing
                browser.tabs.sendMessage(tab.id, {
                    type: 'processPrompt',
                    prompt: info.selectionText,
                    autoScroll: true
                });
            }
        });
    }
}

// Initialize background manager
const backgroundManager = new BackgroundManager();

// Optional: Set up context menu
// backgroundManager.setupContextMenu();
